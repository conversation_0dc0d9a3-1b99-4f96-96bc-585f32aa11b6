# TODOS - TrainerBattleScreen XP-System Bugfixes

## Projektübersicht
Behebung der XP-Anzeige und Animation im TrainerBattleScreen, damit sie identisch zum BattleScreen funktioniert.

## 🔧 XP-System Implementierung

### **XP-Bar Rendering korrigieren**
- [x] **Problem**: TrainerBattleScreen zeigt keine blaue XP-Leiste
- [x] **Lösung**: `updateExpBar()` Funktion aus `battle-utils.js` in `updatePokemonCard()` verwenden
- [x] **Details**:
  - In `updatePokemonCard()` Methode (Zeile ~520) für 'player' side
  - `updateExpBar(expContainer, pokemon)` statt manueller XP-Text Erstellung
  - Sicherstellen dass `expContainer` korrekt identifiziert wird
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: Blaue XP-Leiste wird korrekt angezeigt

### **XP-Container HTML-Struktur korrigieren**
- [ ] **Problem**: HTML-Template verwendet falsche XP-Container Struktur
- [ ] **Lösung**: XP-Container HTML an BattleScreen.js Template anpassen
- [ ] **Details**:
  - In `createBattleScreen()` Methode (Zeile ~200)
  - `<div id="player-exp-container" class="pokemon-exp-container">` muss identisch zu BattleScreen sein
  - Keine vorgefertigten XP-Elemente im HTML, da diese dynamisch generiert werden
- [ ] **Dateien**: TrainerBattleScreen.js
- [ ] **Akzeptanzkriterien**: XP-Container hat gleiche Struktur wie BattleScreen

### **XP-Animation nach Rundensieg implementieren**
- [x] **Problem**: `awardRoundXP()` verwendet nicht die gleichen Animation-Funktionen wie BattleScreen
- [x] **Lösung**: XP-Bar Animation aus BattleScreen.js übernehmen
- [x] **Details**:
  - In `awardRoundXP()` Methode (Zeile ~700)
  - Nach `pokemon.addExperience()` die XP-Bar mit `updateExpBar()` aktualisieren
  - Animation für XP-Gewinn implementieren (halbtransparenter blauer Balken)
  - `calculateExpProgress()` Funktion aus BattleScreen.js importieren und verwenden
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: XP-Bar animiert sich nach Rundensieg wie im BattleScreen

### **Missing Import für XP-Funktionen hinzufügen**
- [x] **Problem**: TrainerBattleScreen importiert nicht alle benötigten XP-Funktionen
- [x] **Lösung**: Fehlende Imports aus `experience-system.js` hinzufügen
- [x] **Details**:
  - `getExpForLevel`, `getExpCurveForRarity`, `getExpProgressPercentage` importieren
  - Diese werden für korrekte XP-Berechnung und -Anzeige benötigt
  - Imports am Anfang der Datei hinzufügen
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: Alle XP-Funktionen sind verfügbar

### **calculateExpProgress() Methode implementieren**
- [x] **Problem**: TrainerBattleScreen hat keine `calculateExpProgress()` Methode
- [x] **Lösung**: Methode aus BattleScreen.js kopieren und anpassen
- [x] **Details**:
  - Komplette `calculateExpProgress()` Methode aus BattleScreen.js (Zeile ~200-280) übernehmen
  - Diese berechnet XP-Progress, Level-up Detection, etc.
  - Wird von `updateExpBar()` und XP-Animationen benötigt
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: XP-Progress wird korrekt berechnet

### **XP-Display Format korrigieren**
- [x] **Problem**: TrainerBattleScreen zeigt Gesamt-XP statt Level-Progress
- [x] **Lösung**: XP-Text Format an BattleScreen anpassen
- [x] **Details**:
  - Statt `pokemon.experience` total XP anzeigen
  - Format: `expInCurrentLevel / expNeededForNextLevel XP` verwenden
  - Berechnung über `calculateExpProgress()` Methode
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: XP-Text zeigt "X / Y XP" Format wie BattleScreen

## 🎬 XP-Animation Implementierung

### **animateExperienceGain() Methode implementieren**
- [x] **Problem**: TrainerBattleScreen hat keine XP-Animation nach Rundensieg
- [x] **Lösung**: `animateExperienceGain()` Methode aus BattleScreen.js adaptieren
- [x] **Details**:
  - Methode aus BattleScreen.js (Zeile ~400-600) übernehmen
  - An Trainer-Kampf Kontext anpassen (Runden statt einzelner Kampf)
  - Nach jedem `awardRoundXP()` Aufruf triggern
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: XP-Animation läuft nach jeder gewonnenen Runde

### **animateExpBar() Methode implementieren**
- [x] **Problem**: Keine XP-Bar Animation Methode vorhanden
- [x] **Lösung**: `animateExpBar()` aus BattleScreen.js übernehmen
- [x] **Details**:
  - Methode für halbtransparente XP-Balken Animation
  - Zeigt visuell die gewonnenen XP an
  - Integration in `animateExperienceGain()` Workflow
- [x] **Dateien**: TrainerBattleScreen.js
- [x] **Akzeptanzkriterien**: Halbtransparenter blauer Balken zeigt XP-Gewinn

### **Level-up Notification System**
- [ ] **Problem**: Level-up Benachrichtigungen funktionieren nicht korrekt
- [ ] **Lösung**: Level-up System aus BattleScreen.js übernehmen
- [ ] **Details**:
  - `showLevelUpNotification()` Methode bereits vorhanden, aber möglicherweise nicht korrekt integriert
  - Sicherstellen dass Level-up nach XP-Animation angezeigt wird
  - Evolution-Check Integration prüfen
- [ ] **Dateien**: TrainerBattleScreen.js
- [ ] **Akzeptanzkriterien**: Level-up wird korrekt angezeigt und animiert

## 🔍 Code-Vergleich und Debugging

### **updateExpBar() Funktion aus battle-utils.js verwenden**
- [x] **Problem**: TrainerBattleScreen verwendet nicht die zentrale `updateExpBar()` Funktion
- [x] **Lösung**: Import und Verwendung von `updateExpBar()` aus battle-utils.js
- [x] **Details**:
  - `updateExpBar(expContainer, pokemon)` in `updatePokemonCard()` verwenden
  - Diese Funktion generiert die komplette XP-Bar HTML-Struktur
  - Ersetzt manuelle XP-Text Erstellung
- [x] **Dateien**: TrainerBattleScreen.js, battle-utils.js
- [x] **Akzeptanzkriterien**: XP-Bar wird identisch zu BattleScreen gerendert

### **CSS-Klassen für XP-Animation prüfen**
- [ ] **Problem**: Möglicherweise fehlen CSS-Klassen für XP-Animation
- [ ] **Lösung**: CSS-Klassen aus battle-screen.css in trainer-battle.css übernehmen
- [ ] **Details**:
  - `.pokemon-exp-fill`, `.pokemon-exp-new`, `.level-up-notification` CSS prüfen
  - Sicherstellen dass XP-Animation CSS verfügbar ist
  - Halbtransparente XP-Balken Styling
- [ ] **Dateien**: trainer-battle.css, battle-screen.css
- [ ] **Akzeptanzkriterien**: XP-Animationen haben korrektes Styling

### **XP-Persistierung nach jeder Runde validieren**
- [ ] **Problem**: XP wird möglicherweise nicht nach jeder Runde gespeichert
- [ ] **Lösung**: `pokemonManager.updatePokemon()` und `pokemonManager.saveTeam()` Aufrufe prüfen
- [ ] **Details**:
  - In `awardRoundXP()` sicherstellen dass XP sofort gespeichert wird
  - Nicht erst am Ende des gesamten Trainerkampfs
  - Debug-Logging für XP-Speicherung hinzufügen
- [ ] **Dateien**: TrainerBattleScreen.js, pokemon-manager.js
- [ ] **Akzeptanzkriterien**: XP wird nach jeder Runde persistent gespeichert

## 🧪 Testing und Validierung

### **XP-System End-to-End Test**
- [ ] **Beschreibung**: Vollständiger Test des XP-Systems im TrainerBattleScreen
- [ ] **Details**:
  - Pokemon mit bekannter XP-Menge in Trainerkampf schicken
  - Mehrere Runden gewinnen und XP-Anzeige validieren
  - Level-up während Trainerkampf testen
  - XP-Persistierung nach Kampfabbruch prüfen
- [ ] **Akzeptanzkriterien**: XP-System funktioniert identisch zu BattleScreen

### **Visual Consistency Check**
- [ ] **Beschreibung**: Visueller Vergleich zwischen BattleScreen und TrainerBattleScreen
- [ ] **Details**:
  - XP-Bar Aussehen und Position
  - XP-Text Format und Inhalt
  - Animation-Timing und -Effekte
  - Level-up Notification Styling
- [ ] **Akzeptanzkriterien**: Beide Screens sehen identisch aus

## Technische Hinweise für Augment Code KI

### **Zu überprüfende Funktionen:**
1. **TrainerBattleScreen.updatePokemonCard()** - Verwendet nicht `updateExpBar()`
2. **TrainerBattleScreen.awardRoundXP()** - Fehlt XP-Animation Aufruf
3. **TrainerBattleScreen.createBattleScreen()** - XP-Container HTML-Struktur
4. **battle-utils.js updateExpBar()** - Zentrale XP-Bar Rendering Funktion
5. **BattleScreen.calculateExpProgress()** - Referenz-Implementierung für XP-Berechnung

### **Fehlende Imports:**
- `getExpForLevel`, `getExpCurveForRarity` aus experience-system.js
- Möglicherweise `calculateExpProgress` als separate Utility-Funktion

### **Code-Duplikation vermeiden:**
- XP-Funktionen in battle-utils.js zentralisieren
- Gleiche XP-Animation Logik in beiden Screens verwenden
- CSS-Klassen zwischen beiden Screens teilen

## Ressourcen
- BattleScreen.js als Referenz für korrektes XP-System
- battle-utils.js für zentrale XP-Funktionen
- experience-system.js für XP-Berechnungen
- battle-screen.css für XP-Animation Styling
